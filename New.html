<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Step Wizard + PDF Preview</title>

 <!-- ✅ Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />

<!-- ✅ Dropzone CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />

<!-- ✅ Load jQuery First (only once) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- ✅ Then jQuery-dependent libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>

<!-- ✅ PDF.js comes last -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>




  <style>
    .wizard-steps {
      display: flex;
      gap: 20px;
      margin: 30px 20px;
      flex-wrap: wrap;
    }

    .wizard-step {
      position: relative;
      display: inline-block;
      padding: 12px 20px;
      min-width: 160px;
      text-align: center;
      background: linear-gradient(to right, #f0f0f0, #e0e0e0);
      clip-path: polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%);
      color: #999;
      font-size: 14px;
      font-weight: normal;
      cursor: pointer;
    }

    .wizard-step label {
      display: block;
      font-weight: bold;
      color: #999;
      margin-bottom: 0;
    }

    .wizard-step span {
      font-size: 13px;
      color: #999;
    }

    .wizard-step.active {
      background: linear-gradient(to right, #e6f0fc, #d6e9f9);
    }

    .wizard-step.active label {
      color: #0056b3;
    }

    .wizard-step.active span {
      color: darkred;
    }

    .step-section {
      padding: 20px;
      margin: 0 20px;
      border: 1px solid #ccc;
      border-radius: 6px;
      background-color: #f9f9f9;
    }

    .d-none {
      display: none !important;
    }

    canvas {
      max-width: 100%;
      border: 1px solid #ccc;
    }

        #step2Content .col-md-4 {
      min-height: 450px; /* Match PDF preview height */
    }

    /* Upload Container Styles */
    .upload-container {
      background-color: #f8f9fa;
      border-color: #dee2e6 !important;
      border-style: dashed !important;
      border-width: 2px !important;
      padding: 2rem !important;
      transition: all 0.3s ease;
    }

    .upload-container:hover {
      border-color: #007bff !important;
      background-color: #f0f8ff;
    }

    .upload-container.dz-drag-hover {
      border-color: #007bff !important;
      background-color: #e3f2fd;
    }

    .upload-icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .upload-text {
      margin-bottom: 0.5rem;
      font-size: 14px;
    }

    .upload-info {
      font-size: 12px;
    }

    /* Hide default dropzone elements */
    .upload-container .dz-message,
    .upload-container .dz-preview,
    .upload-container .dz-default {
      display: none !important;
    }

    /* Ensure our custom content is visible */
    .upload-container .upload-icon,
    .upload-container .upload-text,
    .upload-container .upload-info {
      display: block !important;
    }

    /* PDF Container with Scrolling */
    .pdf-container {
      background-color: #ffffff;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 15px;
      min-height: 500px;
      max-height: 600px;
      overflow: auto; /* Enable both horizontal and vertical scrolling */
      position: relative;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .pdf-container::-webkit-scrollbar {
      width: 12px;
      height: 12px;
    }

    .pdf-container::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .pdf-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 6px;
    }

    .pdf-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .pdf-preview-area {
      min-width: 800px; /* Ensure minimum width for horizontal scrolling */
      min-height: 600px; /* Ensure minimum height for vertical scrolling */
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }

    #pdfCanvas, #pdfCanvasStep2 {
      border: 1px solid #ddd;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      background: white;
      margin: 10px 0;
    }

    .pdf-controls {
      background: rgba(255,255,255,0.95);
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 8px 12px;
      margin-top: 15px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.1);
      position: sticky;
      bottom: 10px;
      z-index: 10;
    }

  </style>
</head>
<body>

<div class="container-fluid mt-4">
  <!-- Step Wizard -->
  <div class="wizard-steps">
    <div class="wizard-step active" data-step="1">
      <label>Step 1</label>
      <span>Add Document</span>
    </div>
    <div class="wizard-step" data-step="2">
      <label>Step 2</label>
      <span>Code Info</span>
    </div>
    <div class="wizard-step" data-step="3">
      <label>Step 3</label>
      <span>Heat No</span>
    </div>
    <div class="wizard-step" data-step="4">
      <label>Step 4</label>
      <span>Edit AI Response</span>
    </div>
  </div>

  <!-- Step 1 -->
  <div class="step-section" id="step1Content">
    <div class="row">
      <!-- PDF Viewer -->
      <div class="col-md-8">
        <div class="pdf-container">
          <div id="pdfPreviewArea" class="pdf-preview-area text-center text-muted">
            <canvas id="pdfCanvas"></canvas>
            <div class="text-muted mt-3" id="emptyState" style="display: none;">
              <i class="fas fa-file-pdf fa-3x mb-3"></i>
              <p>Upload a PDF document to preview</p>
            </div>
          </div>
          <div class="pdf-controls d-flex gap-2 justify-content-center">
            <button id="prevPage" class="btn btn-secondary btn-sm">
              <i class="fas fa-chevron-left"></i> Prev
            </button>
            <button id="nextPage" class="btn btn-secondary btn-sm">
              Next <i class="fas fa-chevron-right"></i>
            </button>
            <span id="pageCount" class="align-self-center mx-3">Page 0 of 0</span>
            <button id="zoomOut" class="btn btn-outline-secondary btn-sm">
              <i class="fas fa-search-minus"></i> Zoom Out
            </button>
            <button id="zoomIn" class="btn btn-outline-secondary btn-sm">
              <i class="fas fa-search-plus"></i> Zoom In
            </button>
          </div>
        </div>
      </div>

      <!-- Upload Form -->
      <div class="col-md-4">
        <h6 class="mb-3">Upload Document</h6>
        <form action="#"
              method="post"
              class="dropzone upload-container border border-2 border-dashed rounded p-4 text-center mb-3"
              id="uploadDropzone">
          <div class="upload-icon mb-3">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
              <line x1="14" y1="8" x2="20" y2="2"/>
              <line x1="17" y1="5" x2="17" y2="5"/>
            </svg>
          </div>
          <div class="upload-text">
            <span class="text-primary fw-medium">Upload a file</span>
            <span class="text-muted"> or drag and drop</span>
          </div>
          <div class="upload-info">
            <small class="text-muted">Multiple HTs and Sizes A312-08, 09 (1).pdf</small>
          </div>
        </form>

        <div class="form-group">
          <label class="control-label">Name <span class="text-danger fw-bold">*</span> :</label>
          <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
        </div>

        <div class="form-group mt-3">
          <label class="control-label">Document Type <span class="text-danger fw-bold">*</span> :</label>
          <div class="row">
            <div class="col-sm-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                <label class="form-check-label" for="rbDocType1">MTR</label>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" value="3" name="rbDoc" id="rbDocType3">
                <label class="form-check-label" for="rbDocType3">WPS</label>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" value="4" name="rbDoc" id="rbDocType4">
                <label class="form-check-label" for="rbDocType4">PQR</label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group mt-3">
          <label class="control-label">Description</label>
          <textarea id="txtDescription" class="form-control" placeholder="Description"></textarea>
        </div>

        <div class="modal-footer mt-4">
          <button type="button" class="btn btn-primary w-100" id="btnTryAI">Run MTR AI Validation</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Step 2 -->
  <%--<div class="step-section d-none" id="step2Content">
    <div class="row">
      <!-- PDF Viewer -->
      <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
        <canvas id="pdfCanvasStep2"></canvas>
      </div>

      <!-- Heat Number List -->
      <!-- Right Column: Heat Number List -->
<div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
  <div>
    <h6>Select Heat Numbers</h6>
    <p class="text-muted">The AI found heat numbers. Select which to include in the review.</p>
    <div id="heatNumberList" class="mb-3"></div>
  </div>
  <div class="mt-auto">
    <button class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
  </div>
</div>

    </div>
  </div>--%>

    <!-- Step 2 -->
<!-- Step 2 -->
<div class="step-section d-none" id="step2Content">
  <div class="row">
    <!-- PDF Viewer -->
    <div class="col-md-8">
      <div class="pdf-container">
        <div class="pdf-preview-area text-center">
          <canvas id="pdfCanvasStep2"></canvas>
        </div>
      </div>
    </div>

    <!-- Right Column: CodeInfo List Only -->
    <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
      <div>
        <!-- ✅ CodeInfo Checkboxes Section -->
        <h6>Select Code Info</h6>
        <p class="text-muted">Select one or more Code Info values identified by AI.</p>
        <div id="codeInfoList" class="mb-3"></div>
      </div>
      <div class="mt-auto">
        <button class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
      </div>
    </div>
  </div>
</div>

<!-- Step 3 -->
<div class="step-section d-none" id="step3Content">
  <%--<h5>Step 3: Edit AI Response</h5>
  <p class="text-muted">Review and confirm the heat numbers identified by AI.</p>--%>

  <!-- ✅ Heat Number Section for Step 3 -->
  <h6 class="mt-4">Select Heat Numbers</h6>
  <p class="text-muted" id="heatNoSummaryStep3"></p>
  <div id="heatNumberListStep3" class="mb-3"></div>

  <!-- Navigation Buttons (Optional) -->
  <%--<div class="d-flex justify-content-end mt-4">
    <button class="btn btn-secondary me-2" id="btnBackToStep2">Back</button>
    <button class="btn btn-primary" id="btnContinueToStep4">Continue to Step 4</button>
  </div>--%>
</div>

<!-- Step 4 -->
<div class="step-section d-none" id="step4Content">
  <h5>Step 4: Process Document</h5>
  <p>This is the content for Step 4.</p>
</div>


<!-- Scripts -->
<script>
    Dropzone.autoDiscover = false;

    let pdfDoc = null;
    let pdfDocData = null;
    let pageNum = 1;
    let pageCount = 0;
    let scale = 1.2;
    const canvas = document.getElementById('pdfCanvas');
    const ctx = canvas.getContext('2d');

    function renderPage(num) {
        pdfDoc.getPage(num).then(function (page) {
            const viewport = page.getViewport({ scale: scale });
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Show canvas and hide empty state
            canvas.style.display = 'block';
            document.getElementById('emptyState').style.display = 'none';

            page.render({ canvasContext: ctx, viewport });
            document.getElementById('pageCount').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
        });
    }

    function renderStep2PDF() {
        if (!pdfDocData) return;
        const canvasStep2 = document.getElementById('pdfCanvasStep2');
        const ctx2 = canvasStep2.getContext('2d');
        pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
            pdf.getPage(1).then(function (page) {
                const viewport = page.getViewport({ scale: 1.2 });
                canvasStep2.width = viewport.width;
                canvasStep2.height = viewport.height;
                page.render({ canvasContext: ctx2, viewport });
            });
        });
    }

    function populateHeatNumbers(heatNumbers) {
        const container = $('#heatNumberList');
        container.empty();
        if (!heatNumbers || heatNumbers.length === 0) {
            container.append('<p class="text-muted">No heat numbers found.</p>');
            return;
        }

        heatNumbers.forEach((heat, index) => {
            const id = `heat_${index}`;
            container.append(`
      <div class="form-check">
        <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
        <label class="form-check-label" for="${id}">${heat}</label>
      </div>
    `);
        });
    }


    $(document).ready(function () {
        // Initialize empty state
        canvas.style.display = 'none';
        document.getElementById('emptyState').style.display = 'block';

        console.log('wizard-step--');
        debugger;
        $('.wizard-step').on('click', function () {
            const step = $(this).data('step');
            $('.wizard-step').removeClass('active');
            $(this).addClass('active');
            $('.step-section').addClass('d-none');
            $('#step' + step + 'Content').removeClass('d-none');

            if (step === 2) {
                renderStep2PDF();

                $.ajax({
                    url: '/api/getHeatNumbers', // your endpoint
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ filename: uploadedFilename }), // or other input
                    success: function (res) {
                        populateHeatNumbers(res.heatNumbers);
                    },
                    error: function () {
                        populateHeatNumbers([]);
                    }
                });
            }

        });

        const myDropzone = new Dropzone("#uploadDropzone", {
            maxFiles: 1,
            maxFilesize: 10,
            acceptedFiles: ".pdf",
            autoProcessQueue: true,
            addRemoveLinks: true,
            dictDefaultMessage: "", // Remove default message
            previewTemplate: '<div style="display:none;"></div>', // Hide preview template
            init: function () {
                // Remove any existing dropzone messages
                this.element.querySelector('.dz-message')?.remove();
                this.on("addedfile", function (file) {
                    $('#btnTryAI').prop('disabled', false);
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        pdfDocData = new Uint8Array(e.target.result);
                        pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                            pdfDoc = pdf;
                            pageCount = pdf.numPages;
                            pageNum = 1;
                            renderPage(pageNum);
                        });
                    };
                    reader.readAsArrayBuffer(file);
                    $('#btnTryAI').data('uploadedFilename', file.name);
                });

                this.on("removedfile", function () {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.style.display = 'none';
                    document.getElementById('emptyState').style.display = 'block';
                    $('#btnTryAI').prop('disabled', true).removeData('uploadedFilename');
                    document.getElementById('pageCount').textContent = 'Page 0 of 0';
                });
            }
        });

        $('#prevPage').on('click', function () {
            if (pageNum <= 1) return;
            pageNum--;
            renderPage(pageNum);
        });

        $('#nextPage').on('click', function () {
            if (pageNum >= pdfDoc.numPages) return;
            pageNum++;
            renderPage(pageNum);
        });

        $('#zoomIn').on('click', function () {
            scale += 0.2;
            renderPage(pageNum);
        });

        $('#zoomOut').on('click', function () {
            if (scale > 0.4) {
                scale -= 0.2;
                renderPage(pageNum);
            }
        });

        $('#btnTryAI').on('click', function () {
            const uploadedFilename = $(this).data('uploadedFilename');
            if (!uploadedFilename) {
                alert('No uploaded file available for validation.');
                return;
            }
            alert("Running AI validation on: " + uploadedFilename);
        });
    });

</script>
</body>
</html>
