<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="YourNamespace.SiteMaster" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%: Page.Title %> - MTR Document Validation</title>

    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dropzone & PDF.js -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>

    <style>
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .wizard-step {
            flex: 1;
            text-align: center;
            padding: 15px 10px;
            background-color: #e9ecef;
            color: #6c757d;
            border-radius: 8px;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-weight: 500;
        }

        .wizard-step.active {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .wizard-step:hover:not(.active) {
            background-color: #dee2e6;
            color: #495057;
        }

        .step-section {
            min-height: 600px;
            background-color: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9ff;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e6f3ff;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background-color: #f0fff4;
        }

        .pdf-viewer {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
            height: 500px;
            overflow: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pdf-canvas {
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            padding: 10px 30px;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .heat-number-item {
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            transition: background-color 0.2s ease;
        }

        .heat-number-item:hover {
            background-color: #e9ecef;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        #step4Content .row {
            margin: 0;
        }

        #step4Content .col-md-7,
        #step4Content .col-md-5 {
            padding: 0;
        }

        .badge {
            border-radius: 0.25rem;
        }

        .pdf-controls {
            background-color: #e5e7eb;
            padding: 12px 20px;
            border-radius: 12px;
            margin-top: 20px;
        }

        .pdf-btn {
            background-color: #d1d5db;
            border: none;
            padding: 6px 14px;
            font-size: 14px;
            color: #1f2937;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .pdf-btn:hover {
            background-color: #9ca3af;
        }

        .pdf-btn:active {
            background-color: #6b7280;
        }

        .page-info {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }
    </style>
</head>

<body style="background-color: #f5f5f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <form id="form1" runat="server">
        <div class="container-fluid">
            <main>
                <asp:ContentPlaceHolder ID="MainContent" runat="server">
                </asp:ContentPlaceHolder>
            </main>
        </div>
    </form>
</body>
</html>
