<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Wizard</title>
    
    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- PDF.js & Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        /* Main Content Styles */
        .main-content {
            width: 100%;
            height: 100vh;
            background: white;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .main-header {
            background-color: #4285f4;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .main-header h5 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .main-body {
            padding: 20px;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 70px);
        }

        .step-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        /* Step Wizard Styles */
        .wizard-steps {
            display: flex;
            gap: 5px;
            margin-bottom: 30px;
            justify-content: flex-start;
            align-items: center;
            padding: 0 20px;
        }

        .wizard-step {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 18px 40px 18px 25px;
            min-width: 180px;
            height: 80px;
            text-align: center;
            background: #d4d4d4;
            color: #888888;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1;
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%);
        }

        .wizard-step:first-child {
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
            border-radius: 8px 0 0 8px;
            padding-left: 25px;
        }

        .wizard-step:last-child {
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%);
            border-radius: 0 8px 8px 0;
            padding-right: 25px;
        }

        .wizard-step.active {
            background: #c8e6ff;
            color: #1976d2;
            font-weight: 600;
            z-index: 3;
        }

        .wizard-step:hover:not(.active) {
            background: #c0c0c0;
            z-index: 2;
        }

        .wizard-step .step-number {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
            line-height: 1;
        }

        .wizard-step .step-title {
            font-size: 12px;
            line-height: 1.2;
            font-weight: 400;
        }

        .wizard-step.active .step-number {
            font-weight: 700;
            color: #1976d2;
        }

        .wizard-step.active .step-title {
            font-weight: 500;
            color: #d32f2f;
        }

        /* PDF Viewer Styles */
        .pdf-viewer-container {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            position: relative;
            height: 100%;
            max-height: calc(90vh - 200px);
            overflow: hidden;
        }

        .pdf-canvas-container {
            flex: 1;
            overflow: auto;
            overflow-x: auto;
            overflow-y: auto;
            width: 100%;
            height: 100%;
            padding: 10px;
            min-height: 500px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            position: relative;
        }

        /* Custom scrollbar styling */
        .pdf-canvas-container::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .pdf-canvas-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }

        .pdf-canvas-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 6px;
        }

        .pdf-canvas-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .pdf-canvas-container::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        .pdf-canvas-container canvas {
            display: block;
            margin: 0 auto;
            border: 1px solid #ddd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            background-color: white;
            max-width: none;
        }

        .pdf-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .pdf-btn {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            padding: 8px 16px;
            font-size: 14px;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .pdf-btn:hover {
            background-color: #dee2e6;
        }

        .pdf-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .pdf-page-info {
            font-size: 14px;
            color: #495057;
            font-weight: 500;
            padding: 8px 16px;
        }

        /* Upload Area Styles */
        .upload-area {
            border: 2px dashed #4285f4;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background-color: #f8f9fa;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }

        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }

        .upload-icon {
            font-size: 48px;
            color: #4285f4;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .upload-subtext {
            color: #9e9e9e;
            font-size: 14px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .required {
            color: #dc3545;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #4285f4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-item input[type="radio"] {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 50%;
            background-color: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .radio-item input[type="radio"]:checked {
            border-color: #4285f4;
            background-color: #4285f4;
        }

        .radio-item input[type="radio"]:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
        }

        .radio-item input[type="radio"]:hover {
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
        }

        .radio-item input[type="radio"]:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
        }

        .radio-item label {
            cursor: pointer;
            font-weight: 500;
            color: #333;
        }

        .btn-primary-custom {
            background-color: #4285f4;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }

        .btn-primary-custom:hover {
            background-color: #3367d6;
        }

        .btn-primary-custom:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
        }

        .d-none {
            display: none !important;
        }

        .row {
            display: flex;
            gap: 20px;
            flex: 1;
            min-height: 0;
        }

        .col-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            height: 100%;
            overflow: hidden;
        }

        .col-right {
            width: 350px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
        }

        @media (max-width: 768px) {
            .row {
                flex-direction: column;
            }

            .col-right {
                width: 100%;
            }

            .wizard-steps {
                flex-wrap: wrap;
                gap: 10px;
            }

            .wizard-step {
                min-width: 140px;
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Document Wizard -->
    <div class="main-content" id="mainContent">
        <!-- Main Header -->
        <div class="main-header">
            <h5>Add New Document</h5>
        </div>

        <!-- Main Body -->
        <div class="main-body">
            <!-- Step Wizard -->
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1">
                    <div class="step-number">Step 1</div>
                    <div class="step-title">Add Document</div>
                </div>
                <div class="wizard-step" data-step="2">
                    <div class="step-number">Step 2</div>
                    <div class="step-title">AI Response Preview</div>
                </div>
                <div class="wizard-step" data-step="3">
                    <div class="step-number">Step 3</div>
                    <div class="step-title">Edit AI Response</div>
                </div>
                <div class="wizard-step" data-step="4">
                    <div class="step-number">Step 4</div>
                    <div class="step-title">Process Document</div>
                </div>
            </div>

            <!-- Step 1 Content -->
            <div class="step-content" id="step1Content">
                <div class="row">
                    <!-- PDF Viewer -->
                    <div class="col-left">
                        <div class="pdf-viewer-container" id="pdfViewerContainer">
                            <div id="pdfPreviewArea">
                                <div class="upload-icon">
                                    <i class="fa fa-plus"></i>
                                </div>
                                <div class="upload-text">Upload a PDF to begin</div>
                            </div>
                            <div class="pdf-canvas-container d-none" id="pdfCanvasContainer">
                                <canvas id="pdfCanvas"></canvas>
                            </div>
                        </div>

                        <!-- PDF Controls -->
                        <div class="pdf-controls d-none" id="pdfControls">
                            <button id="zoomOut" class="pdf-btn">Zoom Out</button>
                            <span id="zoomLevel" class="pdf-page-info">120%</span>
                            <button id="zoomIn" class="pdf-btn">Zoom In</button>
                            <span id="pageCount" class="pdf-page-info">Page 1 of 1</span>
                            <button id="prevPage" class="pdf-btn">Prev</button>
                            <button id="nextPage" class="pdf-btn">Next</button>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="col-right">
                        <!-- Upload Area -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fa fa-upload"></i>
                            </div>
                            <div class="upload-text">Upload a file or drag and drop</div>
                            <div class="upload-subtext">(PDF up to 10MB)</div>
                            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                        </div>

                        <!-- Form Fields -->
                        <div class="form-group">
                            <label class="form-label">Name <span class="required">*</span>:</label>
                            <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                        </div>

                        <div class="form-group">
                            <label class="form-label">Document Type <span class="required">*</span>:</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label for="rbDocType1">MTR</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label for="rbDocType3">WPS</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description:</label>
                            <textarea id="txtDescription" class="form-control" placeholder="Description" rows="4"></textarea>
                        </div>

                        <button type="button" class="btn-primary-custom" id="btnTryAI" disabled onclick="handleAIValidation()">Run MTR AI Validation</button>
                    </div>
                </div>
            </div>

            <!-- Step 2 Content -->
            <div class="step-content d-none" id="step2Content">
                <div class="row">
                    <div class="col-left">
                        <div class="pdf-viewer-container">
                            <div class="pdf-canvas-container">
                                <canvas id="pdfCanvasStep2"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-right">
                        <h6>Select Heat Numbers</h6>
                        <p style="color: #6c757d;">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" style="margin-bottom: 20px;"></div>
                        <button class="btn-primary-custom" onclick="continueToStep3()">Continue to Review</button>
                    </div>
                </div>
            </div>

            <!-- Step 3 Content -->
            <div class="step-content d-none" id="step3Content">
                <h5>Step 3: Edit AI Response</h5>
                <p>This is the content for Step 3.</p>
                <div class="form-group">
                    <label class="form-label">AI Response:</label>
                    <textarea id="txtAIResponse" class="form-control" rows="10"></textarea>
                </div>
                <button class="btn-primary-custom" onclick="saveResponse()">Save & Continue</button>
            </div>

            <!-- Step 4 Content -->
            <div class="step-content d-none" id="step4Content">
                <h5>Step 4: Process Document</h5>
                <p>This is the content for Step 4.</p>
                <div class="form-group">
                    <label class="form-label">Final Notes:</label>
                    <textarea id="txtFinalNotes" class="form-control" rows="5"></textarea>
                </div>
                <button class="btn-primary-custom" onclick="processDocument()">Process Document</button>
            </div>
        </div>
    </div>

    <script>
        // Configure PDF.js
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.worker.min.js';

        // Global variables
        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        let uploadedFilename = null;

        // PDF functions
        function updateZoomLevel() {
            const zoomElement = document.getElementById('zoomLevel');
            if (zoomElement) {
                zoomElement.textContent = Math.round(scale * 100) + '%';
            }
        }

        function renderPage(num, canvasId = 'pdfCanvas') {
            if (!pdfDoc) return;

            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            pdfDoc.getPage(num).then(function(page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                page.render({ canvasContext: ctx, viewport });

                if (canvasId === 'pdfCanvas') {
                    const pageCount = document.getElementById('pageCount');
                    if (pageCount) {
                        pageCount.textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
                    }
                }
            });
        }

        function showPDFViewer() {
            document.getElementById('pdfPreviewArea').classList.add('d-none');
            document.getElementById('pdfCanvasContainer').classList.remove('d-none');
            document.getElementById('pdfControls').classList.remove('d-none');
            updateZoomLevel();
        }

        function handleFileUpload(file) {
            if (file && file.type === 'application/pdf') {
                uploadedFilename = file.name;
                document.getElementById('btnTryAI').disabled = false;

                const reader = new FileReader();
                reader.onload = function(e) {
                    pdfDocData = new Uint8Array(e.target.result);
                    pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function(pdf) {
                        pdfDoc = pdf;
                        pageCount = pdf.numPages;
                        pageNum = 1;
                        showPDFViewer();
                        renderPage(pageNum);
                        console.log('PDF loaded successfully');
                    }).catch(function(error) {
                        console.error('Error loading PDF:', error);
                        alert('Error loading PDF file: ' + error.message);
                    });
                };
                reader.readAsArrayBuffer(file);
            } else {
                alert('Please select a valid PDF file.');
            }
        }

        function switchStep(step) {
            // Update wizard steps
            document.querySelectorAll('.wizard-step').forEach(s => s.classList.remove('active'));
            const targetStep = document.querySelector(`[data-step="${step}"]`);
            if (targetStep) {
                targetStep.classList.add('active');
            }

            // Update content
            document.querySelectorAll('.step-content').forEach(s => s.classList.add('d-none'));
            const targetContent = document.getElementById(`step${step}Content`);
            if (targetContent) {
                targetContent.classList.remove('d-none');
            }

            // Special handling for step 2
            if (step === 2) {
                renderStep2PDF();
                setTimeout(() => {
                    populateHeatNumbers(['HN001', 'HN002', 'HN003']);
                }, 500);
            }
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;

            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            if (!canvasStep2) return;

            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function(pdf) {
                pdf.getPage(1).then(function(page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function populateHeatNumbers(heatNumbers) {
            const container = document.getElementById('heatNumberList');
            if (!container) return;

            container.innerHTML = '';

            if (!heatNumbers || heatNumbers.length === 0) {
                container.innerHTML = '<p style="color: #6c757d;">No heat numbers found.</p>';
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                const div = document.createElement('div');
                div.style.marginBottom = '10px';
                div.innerHTML = `
                    <input type="checkbox" value="${heat}" id="${id}" checked style="margin-right: 8px;">
                    <label for="${id}" style="cursor: pointer;">${heat}</label>
                `;
                container.appendChild(div);
            });
        }

        // Event handlers
        function handleAIValidation() {
            if (!uploadedFilename) {
                alert('Please upload a PDF file first.');
                return false;
            }

            alert("🤖 Running AI validation on: " + uploadedFilename);
            switchStep(2);
            return false;
        }

        function continueToStep3() {
            const checkboxes = document.querySelectorAll('#heatNumberList input[type="checkbox"]:checked');
            const selectedHeatNumbers = Array.from(checkboxes).map(cb => cb.value);

            console.log('Selected heat numbers:', selectedHeatNumbers);
            switchStep(3);
            return false;
        }

        function saveResponse() {
            const response = document.getElementById('txtAIResponse').value;
            if (!response.trim()) {
                alert('Please enter an AI response.');
                return false;
            }

            alert('💾 AI Response saved successfully!');
            switchStep(4);
            return false;
        }

        function processDocument() {
            const docName = document.getElementById('txtName').value;
            const response = document.getElementById('txtAIResponse').value;

            if (!docName.trim()) {
                alert('Please enter a document name.');
                switchStep(1);
                return false;
            }

            if (!response.trim()) {
                alert('Please complete the AI response step.');
                switchStep(3);
                return false;
            }

            alert('🎉 Document processed successfully!\n\n' +
                  'Document: ' + docName + '\n' +
                  'Type: ' + document.querySelector('input[name="rbDoc"]:checked').nextElementSibling.textContent + '\n' +
                  'This would normally save to your database.');
            return false;
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // File upload functionality
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            if (uploadArea && fileInput) {
                uploadArea.addEventListener('click', () => fileInput.click());

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0]);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleFileUpload(e.target.files[0]);
                    }
                });
            }

            // PDF controls
            document.getElementById('prevPage')?.addEventListener('click', () => {
                if (pageNum <= 1) return;
                pageNum--;
                renderPage(pageNum);
            });

            document.getElementById('nextPage')?.addEventListener('click', () => {
                if (pageNum >= pdfDoc.numPages) return;
                pageNum++;
                renderPage(pageNum);
            });

            document.getElementById('zoomIn')?.addEventListener('click', () => {
                scale += 0.2;
                updateZoomLevel();
                renderPage(pageNum);
            });

            document.getElementById('zoomOut')?.addEventListener('click', () => {
                if (scale > 0.4) {
                    scale -= 0.2;
                    updateZoomLevel();
                    renderPage(pageNum);
                }
            });

            // Wizard navigation
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.addEventListener('click', () => {
                    const stepNum = step.getAttribute('data-step');
                    switchStep(parseInt(stepNum));
                });
            });

            console.log('Document Wizard initialized successfully!');
        });
    </script>
</body>
</html>
