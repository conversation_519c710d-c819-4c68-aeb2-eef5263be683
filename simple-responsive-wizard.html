<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Upload Wizard - Simple Responsive</title>
    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    
    <style>
        /* Reset and Base Styles */
        * { box-sizing: border-box; }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .wizard-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1400px;
            height: 90vh;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
        }

        .wizard-header {
            background-color: #4285f4;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .wizard-body {
            padding: 20px;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Step Wizard Styles */
        .wizard-steps {
            display: flex;
            gap: 5px;
            margin-bottom: 30px;
            justify-content: flex-start;
            align-items: center;
        }

        .wizard-step {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 18px 40px 18px 25px;
            min-width: 180px;
            height: 80px;
            text-align: center;
            background: #d4d4d4;
            color: #888888;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1;
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%);
        }

        .wizard-step:first-child {
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
            border-radius: 8px 0 0 8px;
        }

        .wizard-step:last-child {
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%);
            border-radius: 0 8px 8px 0;
        }

        .wizard-step.active {
            background: #c8e6ff;
            color: #1976d2;
            font-weight: 600;
            z-index: 3;
        }

        .wizard-step:hover:not(.active) {
            background: #c0c0c0;
            z-index: 2;
        }

        .wizard-step .step-number {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .wizard-step .step-title {
            font-size: 12px;
            font-weight: 400;
        }

        .wizard-step.active .step-title {
            color: #d32f2f;
            font-weight: 500;
        }

        /* Content Area */
        .step-content {
            flex: 1;
            display: none;
        }

        .step-content.active {
            display: flex;
            flex-direction: column;
        }

        .content-row {
            display: flex;
            gap: 20px;
            flex: 1;
            min-height: 0;
        }

        .content-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-right {
            width: 350px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
        }

        /* Upload Area */
        .upload-area {
            border: 2px dashed #4285f4;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background-color: #f8f9fa;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover, .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }

        .upload-icon {
            font-size: 48px;
            color: #4285f4;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .upload-subtext {
            color: #9e9e9e;
            font-size: 14px;
        }

        /* PDF Viewer */
        .pdf-viewer-container {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            min-height: 400px;
            position: relative;
        }

        .pdf-preview-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .pdf-preview-area.hidden {
            display: none;
        }

        .pdf-display {
            display: none;
            width: 100%;
            height: 100%;
            overflow: auto;
            padding: 10px;
        }

        .pdf-display.active {
            display: block;
        }

        .pdf-display iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 4px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .required {
            color: #dc3545;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #4285f4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        /* Radio Buttons */
        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-item input[type="radio"] {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 50%;
            background-color: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .radio-item input[type="radio"]:checked {
            border-color: #4285f4;
            background-color: #4285f4;
        }

        .radio-item input[type="radio"]:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
        }

        .radio-item label {
            cursor: pointer;
            font-weight: 500;
            color: #333;
        }

        /* Button */
        .btn-primary {
            background-color: #4285f4;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }

        .btn-primary:hover {
            background-color: #3367d6;
        }

        .btn-primary:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
        }

        .file-info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #2e7d32;
        }

        .heat-number-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .heat-number-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .heat-number-item label {
            cursor: pointer;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .wizard-container {
                max-width: 100%;
                margin: 10px;
                height: calc(100vh - 20px);
            }

            .content-right {
                width: 320px;
            }
        }

        @media (max-width: 992px) {
            .wizard-steps {
                gap: 3px;
                margin-bottom: 20px;
            }

            .wizard-step {
                min-width: 140px;
                padding: 15px 30px 15px 20px;
                height: 70px;
                font-size: 13px;
            }

            .content-right {
                width: 280px;
            }

            .wizard-body {
                padding: 15px;
            }
        }

        @media (max-width: 768px) {
            .wizard-container {
                margin: 5px;
                height: calc(100vh - 10px);
                border-radius: 4px;
            }

            .wizard-header {
                padding: 12px 15px;
                font-size: 16px;
            }

            .wizard-body {
                padding: 10px;
            }

            .wizard-steps {
                flex-wrap: wrap;
                justify-content: center;
                gap: 8px;
                margin-bottom: 15px;
            }

            .wizard-step {
                min-width: 120px;
                padding: 12px 20px 12px 15px;
                height: 60px;
                font-size: 12px;
                flex: 1;
                max-width: 150px;
            }

            .content-row {
                flex-direction: column;
                gap: 15px;
            }

            .content-left {
                order: 2;
            }

            .content-right {
                order: 1;
                width: 100%;
                flex-shrink: 1;
            }

            .pdf-viewer-container {
                min-height: 300px;
            }

            .upload-area {
                padding: 30px 15px;
            }

            .upload-icon {
                font-size: 36px;
            }

            .upload-text {
                font-size: 14px;
            }

            .radio-group {
                gap: 15px;
            }
        }

        @media (max-width: 576px) {
            .wizard-steps {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .wizard-step {
                width: 100%;
                max-width: 280px;
                clip-path: none;
                border-radius: 6px;
                height: 50px;
                padding: 10px 15px;
            }

            .wizard-step:first-child,
            .wizard-step:last-child {
                clip-path: none;
                border-radius: 6px;
            }

            .wizard-header {
                padding: 10px 12px;
                font-size: 15px;
            }

            .wizard-body {
                padding: 8px;
            }

            .content-row {
                gap: 10px;
            }

            .pdf-viewer-container {
                min-height: 250px;
            }

            .upload-area {
                padding: 25px 10px;
            }

            .upload-icon {
                font-size: 32px;
                margin-bottom: 10px;
            }

            .upload-text {
                font-size: 13px;
            }

            .upload-subtext {
                font-size: 12px;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-label {
                font-size: 14px;
                margin-bottom: 6px;
            }

            .form-control {
                padding: 8px 10px;
                font-size: 14px;
            }

            .radio-group {
                flex-direction: column;
                gap: 10px;
            }

            .btn-primary {
                padding: 10px 20px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .wizard-container {
                margin: 2px;
                height: calc(100vh - 4px);
                border-radius: 2px;
            }

            .wizard-step {
                max-width: 250px;
                height: 45px;
                font-size: 11px;
            }

            .wizard-step .step-number {
                font-size: 11px;
                margin-bottom: 2px;
            }

            .wizard-step .step-title {
                font-size: 10px;
            }

            .pdf-viewer-container {
                min-height: 200px;
            }

            .upload-area {
                padding: 20px 8px;
            }

            .upload-icon {
                font-size: 28px;
            }

            .form-control {
                font-size: 13px;
            }
        }

        /* Landscape orientation adjustments for mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .wizard-container {
                height: calc(100vh - 5px);
            }

            .content-row {
                flex-direction: row;
            }

            .content-left {
                order: 1;
                flex: 1;
            }

            .content-right {
                order: 2;
                width: 300px;
            }

            .pdf-viewer-container {
                min-height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="wizard-header">
            Add New Document
        </div>

        <div class="wizard-body">
            <!-- Step Navigation -->
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1">
                    <div class="step-number">Step 1</div>
                    <div class="step-title">Add Document</div>
                </div>
                <div class="wizard-step" data-step="2">
                    <div class="step-number">Step 2</div>
                    <div class="step-title">AI Response Preview</div>
                </div>
                <div class="wizard-step" data-step="3">
                    <div class="step-number">Step 3</div>
                    <div class="step-title">Edit AI Response</div>
                </div>
                <div class="wizard-step" data-step="4">
                    <div class="step-number">Step 4</div>
                    <div class="step-title">Process Document</div>
                </div>
            </div>

            <!-- Step 1 Content -->
            <div class="step-content active" id="step1Content">
                <div class="content-row">
                    <!-- PDF Viewer -->
                    <div class="content-left">
                        <div class="pdf-viewer-container" id="pdfViewerContainer">
                            <div class="pdf-preview-area" id="pdfPreviewArea">
                                <div class="upload-icon">
                                    <i class="fa fa-plus"></i>
                                </div>
                                <div class="upload-text">Upload a PDF to begin</div>
                            </div>
                            <div class="pdf-display" id="pdfDisplay">
                                <iframe id="pdfFrame" src=""></iframe>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="content-right">
                        <!-- Upload Area -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fa fa-upload"></i>
                            </div>
                            <div class="upload-text">Upload a file or drag and drop</div>
                            <div class="upload-subtext">(PDF up to 10MB)</div>
                            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                        </div>

                        <!-- File Info -->
                        <div class="file-info" id="fileInfo" style="display: none;">
                            <i class="fa fa-file-pdf"></i>
                            <span id="fileName">No file selected</span>
                        </div>

                        <!-- Form Fields -->
                        <div class="form-group">
                            <label class="form-label">Name <span class="required">*</span>:</label>
                            <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                        </div>

                        <div class="form-group">
                            <label class="form-label">Document Type <span class="required">*</span>:</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label for="rbDocType1">MTR</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label for="rbDocType3">WPS</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description:</label>
                            <textarea id="txtDescription" class="form-control" placeholder="Description" rows="4"></textarea>
                        </div>

                        <button type="button" class="btn-primary" id="btnTryAI" disabled>Run MTR AI Validation</button>
                    </div>
                </div>
            </div>

            <!-- Step 2 Content -->
            <div class="step-content" id="step2Content">
                <div class="content-row">
                    <div class="content-left">
                        <div class="pdf-viewer-container">
                            <div class="pdf-display active">
                                <iframe id="pdfFrameStep2" src=""></iframe>
                            </div>
                        </div>
                    </div>
                    <div class="content-right">
                        <h6>Select Heat Numbers</h6>
                        <p style="color: #6c757d;">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" style="margin-bottom: 20px;"></div>
                        <button class="btn-primary" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>

            <!-- Step 3 Content -->
            <div class="step-content" id="step3Content">
                <div style="padding: 20px; text-align: center;">
                    <h5>Step 3: Edit AI Response</h5>
                    <p>This is the content for Step 3 - Edit AI Response functionality would be implemented here.</p>
                    <button class="btn-primary" onclick="switchStep(4)" style="max-width: 300px;">Continue to Process</button>
                </div>
            </div>

            <!-- Step 4 Content -->
            <div class="step-content" id="step4Content">
                <div style="padding: 20px; text-align: center;">
                    <h5>Step 4: Process Document</h5>
                    <p>This is the content for Step 4 - Document processing functionality would be implemented here.</p>
                    <div style="margin-top: 30px;">
                        <i class="fa fa-check-circle" style="font-size: 48px; color: #4caf50; margin-bottom: 15px;"></i>
                        <p style="color: #4caf50; font-weight: 600;">Document processing complete!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let uploadedFile = null;
        let uploadedFilename = null;

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple responsive wizard initialized');
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // File upload functionality
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            if (uploadArea && fileInput) {
                uploadArea.addEventListener('click', () => {
                    fileInput.click();
                });

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0]);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleFileUpload(e.target.files[0]);
                    }
                });
            }

            // Wizard step navigation
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.addEventListener('click', () => {
                    const stepNum = step.getAttribute('data-step');
                    switchStep(parseInt(stepNum));
                });
            });

            // AI validation button
            const btnTryAI = document.getElementById('btnTryAI');
            if (btnTryAI) {
                btnTryAI.addEventListener('click', () => {
                    if (!uploadedFilename) {
                        alert('Please upload a PDF file first.');
                        return;
                    }
                    alert("Running AI validation on: " + uploadedFilename);
                    switchStep(2);
                });
            }

            // Continue to review button
            const btnContinueReview = document.getElementById('btnContinueReview');
            if (btnContinueReview) {
                btnContinueReview.addEventListener('click', () => {
                    switchStep(3);
                });
            }
        }

        function handleFileUpload(file) {
            console.log('File upload started:', file ? file.name : 'No file');

            if (file && file.type === 'application/pdf') {
                uploadedFile = file;
                uploadedFilename = file.name;

                // Enable the AI validation button
                const btnTryAI = document.getElementById('btnTryAI');
                if (btnTryAI) {
                    btnTryAI.disabled = false;
                }

                // Show file info
                const fileInfo = document.getElementById('fileInfo');
                const fileName = document.getElementById('fileName');
                if (fileInfo && fileName) {
                    fileName.textContent = file.name;
                    fileInfo.style.display = 'block';
                }

                // Create object URL for PDF display
                const fileURL = URL.createObjectURL(file);

                // Show PDF in iframe
                showPDFViewer(fileURL);

                console.log('PDF file loaded successfully');
            } else {
                alert('Please select a valid PDF file.');
            }
        }

        function showPDFViewer(fileURL) {
            const pdfPreviewArea = document.getElementById('pdfPreviewArea');
            const pdfDisplay = document.getElementById('pdfDisplay');
            const pdfFrame = document.getElementById('pdfFrame');

            if (pdfPreviewArea) pdfPreviewArea.classList.add('hidden');
            if (pdfDisplay) pdfDisplay.classList.add('active');
            if (pdfFrame) pdfFrame.src = fileURL;
        }

        function switchStep(step) {
            console.log('Switching to step:', step);

            // Update wizard steps
            document.querySelectorAll('.wizard-step').forEach(s => s.classList.remove('active'));
            const targetStep = document.querySelector(`[data-step="${step}"]`);
            if (targetStep) {
                targetStep.classList.add('active');
            }

            // Update content
            document.querySelectorAll('.step-content').forEach(s => s.classList.remove('active'));
            const targetContent = document.getElementById(`step${step}Content`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Special handling for step 2
            if (step === 2) {
                renderStep2PDF();
                setTimeout(() => {
                    populateHeatNumbers(['HN001', 'HN002', 'HN003', 'HN004', 'HN005']);
                }, 500);
            }
        }

        function renderStep2PDF() {
            if (!uploadedFile) return;

            const pdfFrameStep2 = document.getElementById('pdfFrameStep2');
            if (pdfFrameStep2 && uploadedFile) {
                const fileURL = URL.createObjectURL(uploadedFile);
                pdfFrameStep2.src = fileURL;
            }
        }

        function populateHeatNumbers(heatNumbers) {
            const container = document.getElementById('heatNumberList');
            if (!container) return;

            container.innerHTML = '';

            if (!heatNumbers || heatNumbers.length === 0) {
                container.innerHTML = '<p style="color: #6c757d;">No heat numbers found.</p>';
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                const div = document.createElement('div');
                div.className = 'heat-number-item';
                div.innerHTML = `
                    <input type="checkbox" value="${heat}" id="${id}" checked>
                    <label for="${id}">${heat}</label>
                `;
                container.appendChild(div);
            });
        }
    </script>
</body>
</html>
