<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="document.aspx.cs" Inherits="YourNamespace.document" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Step Wizard + PDF Preview</title>

    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dropzone & PDF.js -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />


    <style>
    .wizard-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .wizard-step {
        flex: 1;
        text-align: center;
        padding: 15px 10px;
        background-color: #e9ecef;
        color: #6c757d;
        border-radius: 8px;
        margin: 0 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        font-weight: 500;
    }

    .wizard-step.active {
        background-color: #007bff;
        color: white;
        border-color: #0056b3;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .wizard-step:hover:not(.active) {
        background-color: #dee2e6;
        color: #495057;
    }

    .step-section {
        min-height: 600px;
        background-color: white;
        border-radius: 10px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .upload-area {
        border: 2px dashed #007bff;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background-color: #f8f9ff;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        border-color: #0056b3;
        background-color: #e6f3ff;
    }

    .upload-area.dragover {
        border-color: #28a745;
        background-color: #f0fff4;
    }

    .pdf-viewer {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background-color: #f8f9fa;
        height: 500px;
        overflow: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pdf-canvas {
        max-width: 100%;
        max-height: 100%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        padding: 10px 30px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #004085;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .heat-number-item {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }

    .heat-number-item:hover {
        background-color: #e9ecef;
    }

    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    #step4Content .row {
        margin: 0;
    }

    #step4Content .col-md-7,
    #step4Content .col-md-5 {
        padding: 0;
    }

    /* Remove any default Bootstrap badge styles that might interfere */
    .badge {
        border-radius: 0.25rem;
    }

    .pdf-controls {
        background-color: #e5e7eb;
        padding: 12px 20px;
        border-radius: 12px;
        margin-top: 20px;
    }

    .pdf-btn {
        background-color: #d1d5db;
        border: none;
        padding: 6px 14px;
        font-size: 14px;
        color: #1f2937;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .pdf-btn:hover {
        background-color: #9ca3af;
    }

    .pdf-btn:active {
        background-color: #6b7280;
    }

    .page-info {
        font-size: 14px;
        color: #374151;
        font-weight: 500;
    }
    </style>
</head>
<body style="background-color: #f5f5f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <form id="form1" runat="server">
        <div class="container-fluid py-4">
            <!-- Wizard Steps -->
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1" id="step1Tab">
                    <i class="fas fa-upload mb-2"></i><br>
                    <strong>Upload Document</strong><br>
                    <small>Upload your PDF file</small>
                </div>
                <div class="wizard-step" data-step="2" id="step2Tab">
                    <i class="fas fa-list-check mb-2"></i><br>
                    <strong>Select Heat Numbers</strong><br>
                    <small>Choose heat numbers to validate</small>
                </div>
                <div class="wizard-step" data-step="3" id="step3Tab">
                    <i class="fas fa-robot mb-2"></i><br>
                    <strong>AI Analysis</strong><br>
                    <small>Review AI validation results</small>
                </div>
                <div class="wizard-step" data-step="4" id="step4Tab">
                    <i class="fas fa-check-circle mb-2"></i><br>
                    <strong>Final Review</strong><br>
                    <small>Approve and generate report</small>
                </div>
            </div>

            <!-- Step 1: Upload Document -->
            <div id="step1Content" class="step-section p-4">
                <div class="row h-100">
                    <div class="col-md-6">
                        <h4 class="mb-4">
                            <i class="fas fa-upload text-primary me-2"></i>
                            Upload MTR Document
                        </h4>
                        
                        <div id="uploadDropzone" class="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>Drag & Drop your PDF file here</h5>
                            <p class="text-muted">or click to browse files</p>
                            <small class="text-muted">Supported format: PDF (Max 10MB)</small>
                        </div>

                        <div class="mt-4">
                            <h6>Upload Instructions:</h6>
                            <ul class="text-muted">
                                <li>Ensure the PDF is clear and readable</li>
                                <li>Multiple pages are supported</li>
                                <li>File size should not exceed 10MB</li>
                                <li>Only PDF format is accepted</li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-eye text-info me-2"></i>
                            Document Preview
                        </h5>
                        
                        <div class="pdf-viewer">
                            <canvas id="pdfCanvas" class="pdf-canvas"></canvas>
                            <div id="noPreview" class="text-center text-muted">
                                <i class="fas fa-file-pdf fa-4x mb-3"></i>
                                <p>No document uploaded yet</p>
                            </div>
                        </div>

                        <div class="pdf-controls d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" id="prevPage" class="pdf-btn me-2">
                                    <i class="fas fa-chevron-left"></i> Prev
                                </button>
                                <button type="button" id="nextPage" class="pdf-btn">
                                    Next <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            
                            <div class="page-info">
                                <span id="pageCount">Page 0 of 0</span>
                            </div>
                            
                            <div>
                                <button type="button" id="zoomOut" class="pdf-btn me-2">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button type="button" id="zoomIn" class="pdf-btn">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Select Heat Numbers -->
            <div id="step2Content" class="step-section p-4 d-none">
                <div class="row h-100">
                    <div class="col-md-6">
                        <h4 class="mb-4">
                            <i class="fas fa-list-check text-primary me-2"></i>
                            Select Heat Numbers
                        </h4>
                        
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Detected Heat Numbers</h6>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="heatNumberList">
                                    <p class="text-muted">Upload a document to detect heat numbers...</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllHeatNumbers()">
                                    <i class="fas fa-check-double me-1"></i>Select All
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAllHeatNumbers()">
                                    <i class="fas fa-times me-1"></i>Deselect All
                                </button>
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="form-label">Additional Information</label>
                            <textarea class="form-control" rows="3" placeholder="Add any specific requirements or notes for validation..."></textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-eye text-info me-2"></i>
                            Document Reference
                        </h5>
                        
                        <div class="pdf-viewer">
                            <canvas id="pdfCanvasStep2" class="pdf-canvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: AI Analysis -->
            <div id="step3Content" class="step-section p-4 d-none">
                <div class="row h-100">
                    <div class="col-md-12">
                        <h4 class="mb-4">
                            <i class="fas fa-robot text-primary me-2"></i>
                            AI Validation Results
                        </h4>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            AI analysis has been completed. Review the results below and make any necessary adjustments.
                        </div>

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Validation Report</h6>
                                <div>
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" id="btnRequestRevision">
                                        <i class="fas fa-edit me-1"></i>Request Revision
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="btnSaveDraft">
                                        <i class="fas fa-save me-1"></i>Save Draft
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <textarea id="aiResponseEditor" class="form-control" rows="15" placeholder="AI validation results will appear here...">
# MTR Validation Report

## Heat Number: 74008
**Material:** ASTM A312-2021 ASME SA312-19 WELDED STAINLESS STEEL PIPE, TP304/TP304L

### Chemical Composition Analysis
- **Carbon**: 0.023% (Required: ≤ 0.024%) ✓ PASS
- **Manganese**: 1.45% (Required: ≤ 1.50%) ✓ PASS
- **Silicon**: 0.42% (Required: ≤ 0.40%) ✗ FAIL
- **Phosphorus**: 0.028% (Required: ≤ 0.045%) ✓ PASS
- **Sulfur**: 0.015% (Required: ≤ 0.030%) ✓ PASS

### Mechanical Testing
- **Tensile Strength**: 620 MPa (Required: ≥ 515 MPa) ✓ PASS
- **Yield Strength**: 310 MPa (Required: ≥ 205 MPa) ✓ PASS
- **Elongation**: 45% (Required: ≥ 35%) ✓ PASS

### Heat Treatment
- **Process**: Solution Annealed at 1050°C ✓ PASS
- **Cooling**: Rapid cooling in water ✓ PASS

## Heat Number: 74009-A
**Status:** All tests PASSED ✓

## Heat Number: 74010-B
**Material:** ASTM A312-2021 ASME SA312-19

### Issues Identified:
- Silicon content slightly exceeds specification limit
- Recommend review of melting process parameters

**Overall Compliance:** 2 of 3 heat numbers fully compliant
                                </textarea>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <button type="button" class="btn btn-success btn-lg me-3" id="btnApproveReport">
                                <i class="fas fa-check me-2"></i>Approve & Generate Report
                            </button>
                            <button type="button" class="btn btn-warning btn-lg">
                                <i class="fas fa-clock me-2"></i>Save for Later Review
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Final Review -->
            <div id="step4Content" class="step-section d-none" style="height: 600px;">
                <div class="row h-100">
                    <!-- PDF Section -->
                    <div class="col-md-7 h-100" style="padding: 0;">
                        <div class="d-flex flex-column h-100">
                            <!-- PDF Header -->
                            <div class="px-3 py-2" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span style="font-size: 14px; font-weight: 600; color: #495057;">Document Preview</span>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- PDF Content -->
                            <div class="flex-grow-1 d-flex align-items-center justify-content-center" style="background-color: #f8f9fa;">
                                <canvas id="pdfCanvasStep4" class="pdf-canvas" style="max-width: 100%; max-height: 100%;"></canvas>
                            </div>

                            <!-- PDF Footer -->
                            <div class="px-3 py-2" style="background-color: #f8f9fa; border-top: 1px solid #dee2e6;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-secondary me-2">
                                            <i class="fas fa-chevron-left"></i> Prev
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                    <span style="font-size: 14px; color: #6c757d;">Page 1 of 1</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compliance Summary Section -->
                    <div class="col-md-5 bg-white" style="border-left: 1px solid #dee2e6;">
                        <!-- Compliance Summary View -->
                        <div id="complianceSummary" class="d-flex flex-column h-100">
                            <!-- Header -->
                            <div class="px-3 py-2" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span style="font-size: 14px; font-weight: 600; color: #495057;">Compliance Summary</span>
                                    <i class="fas fa-chevron-up" style="color: #6c757d; font-size: 12px;"></i>
                                </div>
                            </div>

                            <!-- Scrollable Content -->
                            <div class="flex-grow-1 overflow-auto" style="padding: 16px 12px;">
                                <!-- Heat No: 74008 Card -->
                                <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                    <!-- Card Header -->
                                    <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px; border-bottom: 1px solid #dee2e6;">
                                        <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74008</span>
                                        <span style="background-color: #dc3545; color: white; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">3 issue(s)</span>
                                    </div>

                                    <!-- Card Content -->
                                    <div style="padding: 16px;">
                                        <!-- ASME Headers with background -->
                                        <div class="d-flex justify-content-end" style="margin-bottom: 12px;">
                                            <div class="text-center" style="width: 80px; margin-right: 8px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">182</div>
                                            </div>
                                            <div class="text-center" style="width: 80px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">240</div>
                                            </div>
                                        </div>

                                        <!-- Test Results -->
                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                            <div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Chemical</div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Comp.</div>
                                            </div>
                                            <div>
                                                <button onclick="showDetailedReview('Chemical Comp.', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Chemical Comp.', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Mech. Testing</div>
                                            <div>
                                                <button onclick="showDetailedReview('Mech. Testing', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Mech. Testing', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0;">
                                            <div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Heat</div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Treatment</div>
                                            </div>
                                            <div>
                                                <button onclick="showDetailedReview('Heat Treatment', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Heat Treatment', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Heat No: 74009-A Card -->
                                <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                    <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px;">
                                        <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74009-A</span>
                                        <span style="background-color: #28a745; color: white; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">All Pass</span>
                                    </div>
                                </div>

                                <!-- Heat No: 74010-B Card -->
                                <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                    <!-- Card Header -->
                                    <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px; border-bottom: 1px solid #dee2e6;">
                                        <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74010-B</span>
                                        <span style="background-color: #ffc107; color: #212529; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">1 issue(s)</span>
                                    </div>

                                    <!-- Card Content -->
                                    <div style="padding: 16px;">
                                        <!-- ASME Headers with background -->
                                        <div class="d-flex justify-content-end" style="margin-bottom: 12px;">
                                            <div class="text-center" style="width: 80px; margin-right: 8px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">182</div>
                                            </div>
                                            <div class="text-center" style="width: 80px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                                <div style="font-size: 12px; color: #6c757d; font-weight: 600;">240</div>
                                            </div>
                                        </div>

                                        <!-- Test Results -->
                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                            <div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Chemical</div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Comp.</div>
                                            </div>
                                            <div>
                                                <button onclick="showDetailedReview('Chemical Comp.', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Chemical Comp.', '74010-B', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                            <div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Mech.</div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Testing</div>
                                            </div>
                                            <div>
                                                <button onclick="showDetailedReview('Mech. Testing', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Mech. Testing', '74010-B', 'ASME SA-240')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0;">
                                            <div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Heat</div>
                                                <div style="font-size: 14px; color: #495057; font-weight: 500;">Treatment</div>
                                            </div>
                                            <div>
                                                <button onclick="showDetailedReview('Heat Treatment', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                                <button onclick="showDetailedReview('Heat Treatment', '74010-B', 'ASME SA-240')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer Button -->
                            <div style="padding: 12px; border-top: 1px solid #dee2e6;">
                                <button style="background-color: #4285f4; color: white; border: none; width: 100%; padding: 10px; border-radius: 4px; font-size: 14px; font-weight: 500;">Finalize Review</button>
                            </div>
                        </div>

                        <!-- Detailed Review View -->
                        <div id="detailedReview" class="d-flex flex-column h-100" style="display: none;">
                            <!-- Header with Back Button -->
                            <div class="px-3 py-2" style="background-color: #4285f4; color: white;">
                                <div class="d-flex align-items-center">
                                    <button onclick="backToSummary()" style="background: none; border: none; color: white; margin-right: 12px; cursor: pointer; font-size: 16px;">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <span style="color: #87ceeb; font-size: 14px;">← Back to Summary</span>
                                </div>
                            </div>

                            <!-- Review Content -->
                            <div class="flex-grow-1 overflow-auto" style="padding: 16px;">
                                <div style="margin-bottom: 16px;">
                                    <h5 id="reviewTitle" style="font-size: 18px; font-weight: 600; color: #212529; margin-bottom: 4px;">Review: Chemical Comp.</h5>
                                    <p id="reviewSubtitle" style="font-size: 14px; color: #6c757d; margin: 0;">For Heat 74008 vs. ASME SA-182</p>
                                </div>

                                <!-- Property Table -->
                                <div style="background-color: white; border: 1px solid #dee2e6; border-radius: 8px;">
                                    <!-- Table Header -->
                                    <div class="d-flex" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 12px; border-radius: 8px 8px 0 0;">
                                        <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">PROPERTY</div>
                                        <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">VALUE FOUND</div>
                                        <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">REQUIRED</div>
                                        <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">STATUS</div>
                                    </div>

                                    <!-- Table Rows -->
                                    <div class="d-flex" style="padding: 12px; border-bottom: 1px solid #e9ecef;">
                                        <div style="flex: 1; font-size: 13px; color: #212529;">Carbon</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">0.023</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">≤ 0.024</div>
                                        <div style="flex: 1;">
                                            <span style="background-color: #d4edda; color: #155724; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✓ Pass</span>
                                        </div>
                                    </div>

                                    <div class="d-flex" style="padding: 12px; border-bottom: 1px solid #e9ecef;">
                                        <div style="flex: 1; font-size: 13px; color: #212529;">Manganese</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">1.45</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">≤ 1.50</div>
                                        <div style="flex: 1;">
                                            <span style="background-color: #d4edda; color: #155724; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✓ Pass</span>
                                        </div>
                                    </div>

                                    <div class="d-flex" style="padding: 12px;">
                                        <div style="flex: 1; font-size: 13px; color: #212529;">Silicon</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">0.42</div>
                                        <div style="flex: 1; font-size: 13px; color: #212529;">≤ 0.40</div>
                                        <div style="flex: 1;">
                                            <span style="background-color: #f8d7da; color: #721c24; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✗ Fail</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>
        Dropzone.autoDiscover = false;

        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        const canvas = document.getElementById('pdfCanvas');
        const ctx = canvas.getContext('2d');

        function renderPage(num) {
            pdfDoc.getPage(num).then(function (page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                page.render({ canvasContext: ctx, viewport });
                document.getElementById('pageCount').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
            });
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;
            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function renderStep4PDF() {
            if (!pdfDocData) return;
            const canvasStep4 = document.getElementById('pdfCanvasStep4');
            const ctx4 = canvasStep4.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep4.width = viewport.width;
                    canvasStep4.height = viewport.height;
                    page.render({ canvasContext: ctx4, viewport });
                });
            });
        }

        function populateHeatNumbers(heatNumbers) {
            const container = $('#heatNumberList');
            container.empty();
            if (!heatNumbers || heatNumbers.length === 0) {
                container.append('<p class="text-muted">No heat numbers found.</p>');
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                container.append(`
        <div class="form-check">
          <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
          <label class="form-check-label" for="${id}">${heat}</label>
        </div>
      `);
            });
        }

        function showDetailedReview(testType, heatNumber, asmeCode) {
            // Hide the compliance summary
            document.getElementById('complianceSummary').style.display = 'none';

            // Show the detailed review
            document.getElementById('detailedReview').style.display = 'block';

            // Update the review content
            document.getElementById('reviewTitle').textContent = `Review: ${testType}`;
            document.getElementById('reviewSubtitle').textContent = `For Heat ${heatNumber} vs. ${asmeCode}`;
        }

        function backToSummary() {
            // Hide the detailed review
            document.getElementById('detailedReview').style.display = 'none';

            // Show the compliance summary
            document.getElementById('complianceSummary').style.display = 'block';
        }

        function selectAllHeatNumbers() {
            $('#heatNumberList input[type="checkbox"]').prop('checked', true);
        }

        function deselectAllHeatNumbers() {
            $('#heatNumberList input[type="checkbox"]').prop('checked', false);
        }

        $(document).ready(function () {
            $('.wizard-step').on('click', function () {
                const step = $(this).data('step');
                $('.wizard-step').removeClass('active');
                $(this).addClass('active');
                $('.step-section').addClass('d-none');
                $('#step' + step + 'Content').removeClass('d-none');

                if (step === 2) {
                    renderStep2PDF();

                    $.ajax({
                        url: '/api/getHeatNumbers',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ filename: uploadedFilename }),
                        success: function (res) {
                            populateHeatNumbers(res.heatNumbers);
                        },
                        error: function () {
                            populateHeatNumbers([]);
                        }
                    });
                } else if (step === 4) {
                    renderStep4PDF();
                }
            });

            const myDropzone = new Dropzone("#uploadDropzone", {
                maxFiles: 1,
                maxFilesize: 10,
                acceptedFiles: ".pdf",
                autoProcessQueue: true,
                addRemoveLinks: true,
                init: function () {
                    this.on("addedfile", function (file) {
                        $('#btnTryAI').prop('disabled', false);
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            pdfDocData = new Uint8Array(e.target.result);
                            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                                pdfDoc = pdf;
                                pageCount = pdf.numPages;
                                pageNum = 1;
                                renderPage(pageNum);
                            });
                        };
                        reader.readAsArrayBuffer(file);
                        $('#btnTryAI').data('uploadedFilename', file.name);
                    });

                    this.on("removedfile", function () {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        $('#btnTryAI').prop('disabled', true).removeData('uploadedFilename');
                        document.getElementById('pageCount').textContent = 'Page 0 of 0';
                    });
                }
            });

            $('#prevPage').on('click', function () {
                if (pageNum <= 1) return;
                pageNum--;
                renderPage(pageNum);
            });

            $('#nextPage').on('click', function () {
                if (pageNum >= pdfDoc.numPages) return;
                pageNum++;
                renderPage(pageNum);
            });

            $('#zoomIn').on('click', function () {
                scale += 0.2;
                renderPage(pageNum);
            });

            $('#zoomOut').on('click', function () {
                if (scale > 0.4) {
                    scale -= 0.2;
                    renderPage(pageNum);
                }
            });

            $('#btnTryAI').on('click', function () {
                const uploadedFilename = $(this).data('uploadedFilename');
                if (!uploadedFilename) {
                    alert('No uploaded file available for validation.');
                    return;
                }
                alert("Running AI validation on: " + uploadedFilename);
            });

            // Step 4 button handlers
            $('#btnApproveReport').on('click', function () {
                const aiResponse = $('#aiResponseEditor').val();
                if (!aiResponse.trim()) {
                    alert('Please review the AI response before approving.');
                    return;
                }
                if (confirm('Are you sure you want to approve and generate the final report?')) {
                    alert('Report approved! Generating final compliance report...');
                    // Here you would typically send the approved response to the server
                }
            });

            $('#btnRequestRevision').on('click', function () {
                const currentResponse = $('#aiResponseEditor').val();
                const revision = prompt('Please specify what needs to be revised:', '');
                if (revision) {
                    alert('Revision request sent to AI. Please wait for updated analysis...');
                    // Here you would send the revision request to the AI service
                }
            });

            $('#btnSaveDraft').on('click', function () {
                const aiResponse = $('#aiResponseEditor').val();
                if (!aiResponse.trim()) {
                    alert('No content to save.');
                    return;
                }
                alert('Draft saved successfully!');
                // Here you would save the current state as a draft
            });
        });
    </script>
</body>
</html>
