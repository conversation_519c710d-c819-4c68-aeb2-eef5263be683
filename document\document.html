<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Step Wizard + PDF Preview</title>

    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dropzone & PDF.js -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />


    <style>
    .wizard-steps {
        display: flex;
        gap: 20px;
        margin: 30px 20px;
        flex-wrap: wrap;
    }

    .wizard-step {
        position: relative;
        display: inline-block;
        padding: 12px 20px;
        min-width: 160px;
        text-align: center;
        background: linear-gradient(to right, #f0f0f0, #e0e0e0);
        clip-path: polygon(0 0, 95% 0, 100% 50%, 95% 100%, 0 100%);
         color: #999;
        font-size: 14px;
        cursor: pointer;
    }

    .wizard-step label {
        font-weight: bold;
        color: #999;
        margin-bottom: 0;
        display: block;
    }

    .wizard-step span {
        font-size: 13px;
        color: #999;
    }

    .wizard-step.active {
        background: linear-gradient(to right, #e6f0fc, #d6e9f9);
    }

    .wizard-step.active label {
        color: #0056b3;
    }

    .wizard-step.active span {
        color: darkred;
    }

    .step-section {
        padding: 20px;
        margin: 0 20px;
        border: 1px solid #ccc;
        border-radius: 6px;
        background-color: #f9f9f9;
    }

    .d-none {
        display: none !important;
    }

    .scrollable-canvas-container {
        height: 565px;
        overflow: auto;
        width: 100%;
        border: 1px solid #ccc;
        padding-bottom: 10px;
        padding-right: 10px;
        position: relative;
    }
    .pdf-viewer-box {
    min-height: 300px; /* decreased height */
    max-width: 1200px; /* increased width */
}

    .scrollable-canvas-container canvas {
        display: block;
        max-width: none;
    }

    #step3Content .col-md-4 {
        min-height: 450px;
    }

    #pageCount {
        font-weight: 500;
    }

    #step4Content .row {
        margin: 0;
    }

    #step4Content .col-md-7,
    #step4Content .col-md-5 {
        padding: 0;
    }

    /* Remove any default Bootstrap badge styles that might interfere */
    .badge {
        border-radius: 0.25rem;
    }

    .pdf-controls {
        background-color: #e5e7eb;
        padding: 12px 20px;
        border-radius: 12px;
        margin-top: 20px;
    }

    .pdf-btn {
        background-color: #d1d5db;
        border: none;
        padding: 6px 14px;
        font-size: 14px;
        color: #1f2937;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .pdf-btn:hover {
        background-color: #cbd5e1;
    }

    .pdf-page {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
    }

    .pdf-btn, .pdf-page {
        line-height: 1.5rem;
    }
</style>

</head>
<body>

    <div class="container-fluid mt-4">
        <!-- Step Wizard -->
        <div class="wizard-steps">
            <div class="wizard-step active" data-step="1">
                <label>Step 1</label>
                <span>Add Document</span>
            </div>
            <div class="wizard-step" data-step="2">
                <label>Step 2</label>
                <span>Code Info</span>
            </div>
            <div class="wizard-step" data-step="3">
                <label>Step 3</label>
                <span>Heat No Info </span>
            </div>
             <div class="wizard-step" data-step="4">
                <label>Step 4</label>
                <span>Edit AI Response</span>
            </div>
        </div>

        <!-- Step 1 -->
        <div class="step-section" id="step1Content">
            <div class="row">
                <!-- PDF Viewer -->
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3 pdf-viewer-box">

                    <div id="pdfPreviewArea" class="text-center w-100">
                        <div class="scrollable-canvas-container">
                            <canvas id="pdfCanvas"></canvas>
                        </div>
                    </div>
                    <div class="pdf-controls d-flex justify-content-center align-items-center gap-4 mt-3">
                        <button id="zoomOut" class="pdf-btn">Zoom Out</button>
                        <span id="pageCount" class="pdf-page">Page 1 of 1</span>
                        <button id="zoomIn" class="pdf-btn">Zoom In</button>
                        <button id="prevPage" class="pdf-btn">Prev</button>
                        <button id="nextPage" class="pdf-btn">Next</button>
                    </div>

                </div>

                <!-- Upload Form -->
                <div class="col-md-4">
                    <form action="#"
                        method="post"
                        class="dropzone border border-2 border-dashed rounded p-4 text-center mb-3"
                        id="uploadDropzone">
                        <i class="fa fa-upload fs-3 mb-2 d-block text-secondary"></i>
                        <span class="text-secondary">Upload a file or drag and drop<br />
                            <small>(PDF up to 10MB)</small></span>
                    </form>

                    <div class="form-group">
                        <label class="control-label">Name <span class="text-danger fw-bold">*</span> :</label>
                        <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                    </div>

                    <div class="form-group mt-3">
                        <label class="control-label">Document Type <span class="text-danger fw-bold">*</span> :</label>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label class="form-check-label" for="rbDocType1">MTR</label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label class="form-check-label" for="rbDocType3">WPS</label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label class="form-check-label" for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label class="control-label">Description</label>
                        <textarea id="txtDescription" class="form-control" placeholder="Description"></textarea>
                    </div>

                    <div class="modal-footer mt-4">
                        <button type="button" class="btn btn-primary w-100" id="btnTryAI">Run MTR AI Validation</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Step 3 -->
        <div class="step-section d-none" id="step2Content">
            <div class="row">
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
                    <div class="scrollable-canvas-container">
                        <canvas id="pdfCanvasStep"></canvas>
                    </div>
                </div>

                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <h6>Select Code Info</h6>
                        <p class="text-muted">Select one or More Code Info values identified by AI.</p>
                        <div id="CodeInfo" class="mb-3"></div>
                    </div>
                    <div class="mt-auto">
                        <button class="btn btn-primary w-100" id="btn1ContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Step 3 -->
        <div class="step-section d-none" id="step3Content">
            <div class="row">
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
                    <div class="scrollable-canvas-container">
                        <canvas id="pdfCanvasStep2"></canvas>
                    </div>
                </div>

                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <h6>Select Heat Numbers</h6>
                        <p class="text-muted">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" class="mb-3"></div>
                    </div>
                    <div class="mt-auto">
                        <button class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4 -->
        <div class="step-section d-none" id="step4Content">
            <div class="row g-0" style="height: 600px;">
                <!-- PDF Viewer Section -->
                <div class="col-md-7 border-end" style="background: #f8f9fa;">
                    <div class="d-flex flex-column h-100">
                        <!-- PDF Header -->
                        <div class="bg-primary text-white p-2 d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Compliance Review</h6>
                            <button type="button" class="btn-close btn-close-white" aria-label="Close"></button>
                        </div>

                        <!-- PDF Content -->
                        <div class="flex-grow-1 p-3 d-flex flex-column align-items-center justify-content-center">
                            <div class="scrollable-canvas-container">
                                <canvas id="pdfCanvasStep4" style="border: 1px solid #dee2e6; background: white;"></canvas>
                            </div>
                        </div>

                        <!-- PDF Controls -->
                        <div class="border-top p-2 d-flex justify-content-between align-items-center bg-white">
                            <button class="btn btn-sm btn-outline-secondary">Zoom Out</button>
                            <span class="text-muted">Page 1 of 1</span>
                            <button class="btn btn-sm btn-outline-secondary">Zoom In</button>
                            <div class="ms-auto">
                                <button class="btn btn-sm btn-outline-secondary me-1">Prev</button>
                                <button class="btn btn-sm btn-outline-secondary">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Compliance Summary Section -->
                <div class="col-md-5 bg-white" style="border-left: 1px solid #dee2e6;">
                    <!-- Compliance Summary View -->
                    <div id="complianceSummary" class="d-flex flex-column h-100">
                        <!-- Header -->
                        <div class="px-3 py-2" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span style="font-size: 14px; font-weight: 600; color: #495057;">Compliance Summary</span>
                                <i class="fas fa-chevron-up" style="color: #6c757d; font-size: 12px;"></i>
                            </div>
                        </div>

                        <!-- Scrollable Content -->
                        <div class="flex-grow-1 overflow-auto" style="padding: 16px 12px;">
                            <!-- Heat No: 74008 Card -->
                            <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                <!-- Card Header -->
                                <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px; border-bottom: 1px solid #dee2e6;">
                                    <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74008</span>
                                    <span style="background-color: #dc3545; color: white; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">3 issue(s)</span>
                                </div>

                                <!-- Card Content -->
                                <div style="padding: 16px;">
                                    <!-- ASME Headers with background -->
                                    <div class="d-flex justify-content-end" style="margin-bottom: 12px;">
                                        <div class="text-center" style="width: 80px; margin-right: 8px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">182</div>
                                        </div>
                                        <div class="text-center" style="width: 80px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">240</div>
                                        </div>
                                    </div>

                                    <!-- Test Results -->
                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Chemical</div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Comp.</div>
                                        </div>
                                        <div>
                                            <button onclick="showDetailedReview('Chemical Comp.', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Chemical Comp.', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <div style="font-size: 14px; color: #495057; font-weight: 500;">Mech. Testing</div>
                                        <div>
                                            <button onclick="showDetailedReview('Mech. Testing', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Mech. Testing', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0;">
                                        <div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Heat</div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Treatment</div>
                                        </div>
                                        <div>
                                            <button onclick="showDetailedReview('Heat Treatment', '74008', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Heat Treatment', '74008', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Heat No: 74009-A Card -->
                            <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px;">
                                    <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74009-A</span>
                                    <span style="background-color: #28a745; color: white; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">All Pass</span>
                                </div>
                            </div>

                            <!-- Heat No: 74010-B Card -->
                            <div style="border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 16px; background-color: white;">
                                <!-- Card Header -->
                                <div class="d-flex justify-content-between align-items-center" style="padding: 12px 16px; border-bottom: 1px solid #dee2e6;">
                                    <span style="font-size: 16px; font-weight: 600; color: #212529;">Heat No: 74010-B</span>
                                    <span style="background-color: #ffc107; color: #212529; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500;">1 issue(s)</span>
                                </div>

                                <!-- Card Content -->
                                <div style="padding: 16px;">
                                    <!-- ASME Headers with background -->
                                    <div class="d-flex justify-content-end" style="margin-bottom: 12px;">
                                        <div class="text-center" style="width: 80px; margin-right: 8px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">182</div>
                                        </div>
                                        <div class="text-center" style="width: 80px; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">ASME SA-</div>
                                            <div style="font-size: 12px; color: #6c757d; font-weight: 600;">240</div>
                                        </div>
                                    </div>

                                    <!-- Test Results -->
                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Chemical</div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Comp.</div>
                                        </div>
                                        <div>
                                            <button onclick="showDetailedReview('Chemical Comp.', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Chemical Comp.', '74010-B', 'ASME SA-240')" style="background-color: #f8d7da; color: #721c24; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✗ Fail</button>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Mech.</div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Testing</div>
                                        </div>
                                        <div>
                                            <button onclick="showDetailedReview('Mech. Testing', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Mech. Testing', '74010-B', 'ASME SA-240')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center" style="padding: 8px 0;">
                                        <div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Heat</div>
                                            <div style="font-size: 14px; color: #495057; font-weight: 500;">Treatment</div>
                                        </div>
                                        <div>
                                            <button onclick="showDetailedReview('Heat Treatment', '74010-B', 'ASME SA-182')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; margin-right: 8px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                            <button onclick="showDetailedReview('Heat Treatment', '74010-B', 'ASME SA-240')" style="background-color: #d4edda; color: #155724; font-size: 12px; padding: 4px 8px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer;">✓ Pass</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Footer Button -->
                        <div style="padding: 12px; border-top: 1px solid #dee2e6;">
                            <button style="background-color: #4285f4; color: white; border: none; width: 100%; padding: 10px; border-radius: 4px; font-size: 14px; font-weight: 500;">Finalize Review</button>
                        </div>
                    </div>

                    <!-- Detailed Review View -->
                    <div id="detailedReview" class="d-flex flex-column h-100" style="display: none;">
                        <!-- Header with Back Button -->
                        <div class="px-3 py-2" style="background-color: #4285f4; color: white;">
                            <div class="d-flex align-items-center">
                                <button onclick="backToSummary()" style="background: none; border: none; color: white; margin-right: 12px; cursor: pointer; font-size: 16px;">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <span style="color: #87ceeb; font-size: 14px;">← Back to Summary</span>
                            </div>
                        </div>

                        <!-- Review Content -->
                        <div class="flex-grow-1 overflow-auto" style="padding: 16px;">
                            <div style="margin-bottom: 16px;">
                                <h5 id="reviewTitle" style="font-size: 18px; font-weight: 600; color: #212529; margin-bottom: 4px;">Review: Chemical Comp.</h5>
                                <p id="reviewSubtitle" style="font-size: 14px; color: #6c757d; margin: 0;">For Heat 74008 vs. ASME SA-182</p>
                            </div>

                            <!-- Property Table -->
                            <div style="background-color: white; border: 1px solid #dee2e6; border-radius: 8px;">
                                <!-- Table Header -->
                                <div class="d-flex" style="background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 12px; border-radius: 8px 8px 0 0;">
                                    <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">PROPERTY</div>
                                    <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">VALUE FOUND</div>
                                    <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">REQUIRED</div>
                                    <div style="flex: 1; font-size: 12px; font-weight: 600; color: #495057;">STATUS</div>
                                </div>

                                <!-- Table Rows -->
                                <div class="d-flex" style="padding: 12px; border-bottom: 1px solid #e9ecef;">
                                    <div style="flex: 1; font-size: 13px; color: #212529;">Carbon</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">0.023</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">≤ 0.024</div>
                                    <div style="flex: 1;">
                                        <span style="background-color: #d4edda; color: #155724; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✓ Pass</span>
                                    </div>
                                </div>

                                <div class="d-flex" style="padding: 12px; border-bottom: 1px solid #e9ecef;">
                                    <div style="flex: 1; font-size: 13px; color: #212529;">Manganese</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">1.45</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">≤ 1.50</div>
                                    <div style="flex: 1;">
                                        <span style="background-color: #d4edda; color: #155724; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✓ Pass</span>
                                    </div>
                                </div>

                                <div class="d-flex" style="padding: 12px;">
                                    <div style="flex: 1; font-size: 13px; color: #212529;">Silicon</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">0.42</div>
                                    <div style="flex: 1; font-size: 13px; color: #212529;">≤ 0.40</div>
                                    <div style="flex: 1;">
                                        <span style="background-color: #f8d7da; color: #721c24; font-size: 11px; padding: 2px 6px; border-radius: 3px; font-weight: 500;">✗ Fail</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    <script>
        Dropzone.autoDiscover = false;

        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        const canvas = document.getElementById('pdfCanvas');
        const ctx = canvas.getContext('2d');

        function renderPage(num) {
            pdfDoc.getPage(num).then(function (page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                page.render({ canvasContext: ctx, viewport });
                document.getElementById('pageCount').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
            });
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;
            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function renderStep4PDF() {
            if (!pdfDocData) return;
            const canvasStep4 = document.getElementById('pdfCanvasStep4');
            const ctx4 = canvasStep4.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep4.width = viewport.width;
                    canvasStep4.height = viewport.height;
                    page.render({ canvasContext: ctx4, viewport });
                });
            });
        }

        function populateHeatNumbers(heatNumbers) {
            const container = $('#heatNumberList');
            container.empty();
            if (!heatNumbers || heatNumbers.length === 0) {
                container.append('<p class="text-muted">No heat numbers found.</p>');
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                container.append(`
        <div class="form-check">
          <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
          <label class="form-check-label" for="${id}">${heat}</label>
        </div>
      `);
            });
        }

        function showDetailedReview(testType, heatNumber, asmeCode) {
            // Hide the compliance summary
            document.getElementById('complianceSummary').style.display = 'none';

            // Show the detailed review
            document.getElementById('detailedReview').style.display = 'block';

            // Update the review content
            document.getElementById('reviewTitle').textContent = `Review: ${testType}`;
            document.getElementById('reviewSubtitle').textContent = `For Heat ${heatNumber} vs. ${asmeCode}`;
        }

        function backToSummary() {
            // Hide the detailed review
            document.getElementById('detailedReview').style.display = 'none';

            // Show the compliance summary
            document.getElementById('complianceSummary').style.display = 'block';
        }

        $(document).ready(function () {
            $('.wizard-step').on('click', function () {
                const step = $(this).data('step');
                $('.wizard-step').removeClass('active');
                $(this).addClass('active');
                $('.step-section').addClass('d-none');
                $('#step' + step + 'Content').removeClass('d-none');

                if (step === 2) {
                    renderStep2PDF();

                    $.ajax({
                        url: '/api/getHeatNumbers',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ filename: uploadedFilename }),
                        success: function (res) {
                            populateHeatNumbers(res.heatNumbers);
                        },
                        error: function () {
                            populateHeatNumbers([]);
                        }
                    });
                } else if (step === 4) {
                    renderStep4PDF();
                }
            });

            const myDropzone = new Dropzone("#uploadDropzone", {
                maxFiles: 1,
                maxFilesize: 10,
                acceptedFiles: ".pdf",
                autoProcessQueue: true,
                addRemoveLinks: true,
                init: function () {
                    this.on("addedfile", function (file) {
                        $('#btnTryAI').prop('disabled', false);
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            pdfDocData = new Uint8Array(e.target.result);
                            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                                pdfDoc = pdf;
                                pageCount = pdf.numPages;
                                pageNum = 1;
                                renderPage(pageNum);
                            });
                        };
                        reader.readAsArrayBuffer(file);
                        $('#btnTryAI').data('uploadedFilename', file.name);
                    });

                    this.on("removedfile", function () {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        $('#btnTryAI').prop('disabled', true).removeData('uploadedFilename');
                        document.getElementById('pageCount').textContent = 'Page 0 of 0';
                    });
                }
            });

            $('#prevPage').on('click', function () {
                if (pageNum <= 1) return;
                pageNum--;
                renderPage(pageNum);
            });

            $('#nextPage').on('click', function () {
                if (pageNum >= pdfDoc.numPages) return;
                pageNum++;
                renderPage(pageNum);
            });

            $('#zoomIn').on('click', function () {
                scale += 0.2;
                renderPage(pageNum);
            });

            $('#zoomOut').on('click', function () {
                if (scale > 0.4) {
                    scale -= 0.2;
                    renderPage(pageNum);
                }
            });

            $('#btnTryAI').on('click', function () {
                const uploadedFilename = $(this).data('uploadedFilename');
                if (!uploadedFilename) {
                    alert('No uploaded file available for validation.');
                    return;
                }
                alert("Running AI validation on: " + uploadedFilename);
            });

            // Step 4 button handlers
            $('#btnApproveReport').on('click', function () {
                const aiResponse = $('#aiResponseEditor').val();
                if (!aiResponse.trim()) {
                    alert('Please review the AI response before approving.');
                    return;
                }
                if (confirm('Are you sure you want to approve and generate the final report?')) {
                    alert('Report approved! Generating final compliance report...');
                    // Here you would typically send the approved response to the server
                }
            });

            $('#btnRequestRevision').on('click', function () {
                const currentResponse = $('#aiResponseEditor').val();
                const revision = prompt('Please specify what needs to be revised:', '');
                if (revision) {
                    alert('Revision request sent to AI. Please wait for updated analysis...');
                    // Here you would send the revision request to the AI service
                }
            });

            $('#btnSaveDraft').on('click', function () {
                const aiResponse = $('#aiResponseEditor').val();
                if (!aiResponse.trim()) {
                    alert('No content to save.');
                    return;
                }
                alert('Draft saved successfully!');
                // Here you would save the current state as a draft
            });
        });


       
    </script>
</body>
</html>
